package admin

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/reservation/models"
	"errors"
	"gorm.io/gorm"
	"time"
)

func SetExtendedLock(r *shared.AppContext, key string, lockUntil *time.Time) result.EmptyResult {
	currentSession, sessErr := getValidSession(r)
	if sessErr != nil {
		return result.ErrorEmpty(sessErr)
	}

	// lockUntil can be nil, which means remove the lock
	if lockUntil != nil && validateFutureTimestamp(lockUntil) != nil {
		return result.ErrorEmpty(validateFutureTimestamp(lockUntil))
	}

	return result.CheckEmpty(r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
		var reservation models.Reservation
		if err := tx.Where("key = ?", key).First(&reservation).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				reservation = models.Reservation{
					Key:           key,
					CurrentEditor: &r.Account().ID,
				}
			} else {
				return err
			}
		}

		if err := reservation.AvailableFor(r.Account().ID, currentSession); err != nil {
			return err
		}

		if err := evaluatePermissions(r, key); err != nil {
			return err
		}

		reservation.ExtendedLock = lockUntil
		if err := tx.Save(&reservation).Error; err != nil {
			return err
		}

		return nil
	}))
}

func ForceClearExtendedLock(r *shared.AppContext, key string) result.EmptyResult {
	if !r.Account().IsTenantAdmin() {
		return result.ErrorEmpty(auth.NewForbiddenError("only tenant admins can force remove extended locks"))
	}

	return result.CheckEmpty(r.TenantDatabase().Model(&models.Reservation{}).Where("key = ?", key).Updates(map[string]interface{}{
		"extended_lock": nil,
	}).Error)
}
