package adminControllers

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	tenantModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/common/services"
	"contentmanager/library/utils"
	"contentmanager/pkgs/auth/permissions/evaluators"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"net/http"
)

type (
	LegacyURLsController struct{}
)

var (
	service = commonServices.ILegacyUrlService()
)

func (l LegacyURLsController) GetLegacyURLsForContentID(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	id, err := uuid.FromString(p["id"])
	if err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	res, err := service.GetAllByContentID(r.TenantDatabase(), id)
	utils.WriteResponseJSON(w, res, err)
}

func (l LegacyURLsController) ReplaceLegacyURLsForContentID(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	id, err := uuid.FromString(p["id"])
	if err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	if err := evaluators.ForActionByID(r, &tenantModels.Content{}, id, "update"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	res := []tenantModels.LegacyURLViewModel{}
	err = json.NewDecoder(r.Request().Body).Decode(&res)
	if err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	err = service.ReplaceAllByContentID(r.TenantDatabase(), id, res)
	utils.WriteResponseJSON(w, struct{}{}, err)
}
