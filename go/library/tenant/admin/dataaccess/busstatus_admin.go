package adminDataaccess

import (
	"contentmanager/library/shared"
	"contentmanager/library/tenant/common/models"
	"contentmanager/logging"
	emailGenerators2 "contentmanager/pkgs/notifications/email_generators"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func GetBusStatusData(dbCon *gorm.DB, siteId uuid.NullUUID, useSiteId bool, ignoreOnTime bool) ([]commonModels.BusStatus, error) {
	var statusChain = make([]commonModels.BusStatus, 0)
	dbQuery := dbCon

	dbQuery = dbQuery.
		Table("bus_status").
		Select("DISTINCT bus_status.*")

	if useSiteId {
		dbQuery = dbQuery.
			Joins("inner join bus_route on bus_route.id = any(bus_status.routes) AND ? = any(bus_route.sites)", siteId)
	} else {
		dbQuery = dbQuery.
			Joins("inner join bus_route on bus_route.id = any(bus_status.routes)")
	}

	if ignoreOnTime {
		dbQuery = dbQuery.
			Where("type != ?", commonModels.OnTime)
	}

	dbQuery = dbQuery.
		Where("bus_route.active = true AND bus_status.active = true").
		Where(" enddate >= now() ").
		Or(" enddate is null ").
		Order(" StartDate desc ").
		Scan(&statusChain)

	return statusChain, nil
}

func GetAllBusStatusData(dbCon *gorm.DB, siteId uuid.NullUUID) ([]commonModels.BusStatus, error) {
	var statusChain = make([]commonModels.BusStatus, 0)
	dbQuery := dbCon
	dbQuery.
		Order(" StartDate desc").
		Find(&statusChain)

	return statusChain, nil
}

func CreateBusStatus(r *shared.AppContext, queryModel commonModels.BusStatus) (commonModels.BusStatus, error) {
	if err := r.TenantDatabase().Create(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, notifyNotifications(r, queryModel)
}

func notifyNotifications(r *shared.AppContext, queryModel commonModels.BusStatus) error {
	config := emailGenerators2.NewDistrictConfigAccessor(r.TenancyDB())
	notificationAlert := emailGenerators2.BusAlertForIssue{
		HasEmailNotification: queryModel.HasEmailNotification,
		BusRouteIDs:          queryModel.Routes,
		BusStatusID:          queryModel.ID,
		Status:               queryModel.Type,
		StartDate:            queryModel.StartDate.Time,
		Description:          queryModel.Description,
		Active:               queryModel.Active,
	}
	warnError := emailGenerators2.CreateBusAlert(r, config, notificationAlert)
	if warnError != nil {
		logging.FromContext(r.Request().Context()).Error().Err(warnError).Msg(fmt.Sprintf("ERROR notifyNotifications: Can't create"))
	}
	return warnError
}

func UpdateBusStatus(dbCon *gorm.DB, queryModel commonModels.BusStatus) (commonModels.BusStatus, error) {
	dbQuery := dbCon
	if err := dbQuery.Save(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}

func DeleteBusStatus(dbCon *gorm.DB, queryModel commonModels.BusStatus) (commonModels.BusStatus, error) {
	dbQuery := dbCon
	queryModel.Active = false

	if err := dbQuery.Save(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}
