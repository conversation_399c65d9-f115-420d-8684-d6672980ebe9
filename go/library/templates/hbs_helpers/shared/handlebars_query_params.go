package shared

import (
	"contentmanager/library/shared/pagx"
	"contentmanager/library/utils/converters"
	"contentmanager/library/utils/slicexx"
	uuid "github.com/satori/go.uuid"
	"gopkg.in/yaml.v2"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

var pattern *regexp.Regexp

func init() {
	v := reflect.ValueOf(HandlebarsQueryParams{})
	typeOfS := v.Type()
	list := ""
	for i := 0; i < v.NumField(); i++ {
		tag := typeOfS.Field(i).Tag.Get("yaml")
		if len(tag) == 0 {
			continue
		}
		list += strings.Split(tag, ",")[0] + "\\s*:|"
	}
	list = "\\b(" + strings.Trim(list, "|") + ")(\\S)"
	pattern = regexp.MustCompile(list)
}

type (
	HandlebarsQueryParams struct {
		Ids              []uuid.UUID `yaml:"ids,omitempty"`
		Owner            *uuid.UUID  `yaml:"owner,omitempty"`
		DepartmentID     *uuid.UUID  `yaml:"departmentID,omitempty"`
		HasTags          []string    `yaml:"hasTags,omitempty"`
		HasAllTags       []string    `yaml:"hasAllTags,omitempty"`
		ExcludeTags      []string    `yaml:"excludeTags,omitempty"`
		SortBy           []string    `yaml:"sortBy,omitempty"`
		Select           string      `yaml:"select,omitempty"`
		Route            string      `yaml:"route,omitempty"`
		ContentType      string      `yaml:"contentType,omitempty"`
		OnlyPinned       bool        `yaml:"onlyPinned,omitempty"`
		ExcludePinned    bool        `yaml:"excludePinned,omitempty"`
		OnlyShared       bool        `yaml:"onlyShared,omitempty"`
		ExcludeShared    bool        `yaml:"excludeShared,omitempty"`
		ExcludeDisplayed bool        `yaml:"excludeDisplayed,omitempty"`
		MarkAsDisplayed  bool        `yaml:"markAsDisplayed,omitempty"`
		Page             int         `yaml:"page,omitempty"`
		PageSize         int         `yaml:"pageSize,omitempty"`
		SearchTerm       string      `yaml:"searchTerm,omitempty"`
		Period           string      `yaml:"period,omitempty"` // supported values: "all", "upcoming". To consider: implement "day", "week", "month"
		StructureIDs     []uuid.UUID `yaml:"structureIDs,omitempty"`
		Filters          []Filter    `yaml:"filters,omitempty" json:"filters"`
	}

	Filter struct {
		Field    string
		Value    interface{}
		Operator string
	}
)

var _ pagx.Paginator = (*HandlebarsQueryParams)(nil)

func (p HandlebarsQueryParams) GetOffset() int {
	return (p.GetPage() - 1) * p.GetPageSize()
}

func (p HandlebarsQueryParams) GetPageSize() int {
	if p.PageSize <= 0 {
		return 10
	}
	return p.PageSize
}
func (p HandlebarsQueryParams) GetPage() int {
	if p.Page <= 0 {
		return 1
	}
	return p.Page
}

func FromJsonString(json string) (HandlebarsQueryParams, error) {
	var params HandlebarsQueryParams
	json = pattern.ReplaceAllString(json, `$1 $2`)
	err := yaml.Unmarshal([]byte(json), &params)

	return params, err
}

func (p HandlebarsQueryParams) GetKey(displayed []string) string {
	var sb strings.Builder
	if len(p.Ids) > 0 {
		sb.WriteString("ids=")
		sb.WriteString(StringsArrayToString(
			converters.MapArray(p.Ids, func(u uuid.UUID) string { return u.String() })) +
			"-")
	}
	if p.Owner != nil {
		sb.WriteString("owner=")
		sb.WriteString(p.Owner.String() + "-")
	}
	if p.DepartmentID != nil {
		sb.WriteString("departmentID=")
		sb.WriteString(p.DepartmentID.String() + "-")
	}
	if len(p.HasTags) > 0 {
		sb.WriteString("hasTags=")
		sb.WriteString(StringsArrayToString(p.HasTags) + "-")
	}
	if len(p.ExcludeTags) > 0 {
		sb.WriteString("excludeTags=")
		sb.WriteString(StringsArrayToString(p.ExcludeTags) + "-")
	}
	if len(p.SortBy) > 0 {
		sb.WriteString("sortBy=")
		sb.WriteString(StringsArrayToString(p.SortBy) + "-")
	}
	if len(p.Select) > 0 {
		sb.WriteString("select=" + p.Select + "-")
	}
	if len(p.Route) > 0 {
		sb.WriteString("path=" + p.Route + "-")
	}
	if len(p.ContentType) > 0 {
		sb.WriteString("contentType=" + p.ContentType + "-")
	}
	if p.OnlyPinned {
		sb.WriteString("OnlyPinned-")
	}
	if p.ExcludePinned {
		sb.WriteString("ExcludePinned-")
	}
	if p.OnlyShared {
		sb.WriteString("OnlyShared-")
	}
	if p.ExcludeShared {
		sb.WriteString("ExcludeShared-")
	}
	if len(p.SearchTerm) > 0 {
		sb.WriteString("searchTerm=" + p.SearchTerm + "-")
	}
	if len(p.Period) > 0 {
		sb.WriteString("period=" + p.Period + "-")
	}
	if len(p.StructureIDs) > 0 {
		sb.WriteString("structureIDs=")
		sb.WriteString(StringsArrayToString(slicexx.Select(p.StructureIDs, func(u uuid.UUID) string { return u.String() })) + "-")
	}
	// we ignore ExcludeDisplayed here, but use displayed []string instead (displayed cause effect on result)
	//if p.ExcludeDisplayed {
	//	sb.WriteString("ExcludeDisplayed-")
	//}
	// MarkAsDisplayed should be ignored (it has no effect on query)
	//if p.MarkAsDisplayed {
	//	sb.WriteString("MarkAsDisplayed-")
	//}
	sb.WriteString("page=" + strconv.Itoa(p.GetPage()) + "-")
	sb.WriteString("pageSize=" + strconv.Itoa(p.GetPageSize()) + "-")

	if len(displayed) > 0 {
		sb.WriteString("displayed=")
		sb.WriteString(StringsArrayToString(displayed))
	}

	return strings.Trim(sb.String(), "-")
}

func StringsArrayToString(ss []string) string {
	sort.Strings(ss)
	var sb strings.Builder
	for _, s := range ss {
		sb.WriteString(s + ",")
	}
	return strings.Trim(sb.String(), ",")
}
