import { ReservableInfo } from '@/pkgs/reservation/useReservableInfo'
import CMDialog from '@/common/components/CMDialog'
import DialogContent from '@mui/material/DialogContent'
import { Alert, Button, FormControlLabel, Stack, Switch } from '@mui/material'
import React, { useState } from 'react'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { useAppContext } from '@/pkgs/auth/atoms'
import { RESERVABLE_API } from '@/common/constants'
import { httpPost } from '@/common/client'
import { notify } from '@/helpers'
import { error } from 'console'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'

export function LockingDialog(props: {
    open: boolean
    onClose: () => void
    reservationKey: string
    isActive: boolean
    reservableInfo: ReservableInfo
}) {
    const appContext = useAppContext()
    const { open, onClose, reservationKey, isActive, reservableInfo } = props
    const reservable = reservableInfo.result.data

    const [isLoading, setIsLoading] = useState(reservableInfo.result.isLoading)
    const [hasExtendedLock, setHasExtendedLock] = useState(Boolean(reservable?.ExtendedLock))
    const [extendedLockEnds, setExtendedLockEnds] = useState(reservable?.ExtendedLock || null)

    const forceClearLock = async () => {
        setIsLoading(true)
        try {
            await httpPost(`${RESERVABLE_API}/lock/force-clear`, { ReservationKey: reservationKey })
            await reservableInfo.result.refetch()
        } catch (error) {
            notify(guessErrorMessage(error), 'error')
        } finally {
            setIsLoading(false)
        }
    }

    if (!open || !reservable) return null
    return (
        <CMDialog open={open} onClose={onClose} title={'Reservation details'} showCloseButton>
            <DialogContent>
                <Stack direction={'column'} spacing={2}>
                    <Alert severity={reservableInfo.color}>{reservableInfo.message}</Alert>
                    {!isActive && (
                        <Alert severity={'info'}>To extend the lock you need to have an active editing session.</Alert>
                    )}
                    <Stack direction={'row'} spacing={2}>
                        <FormControlLabel
                            control={<Switch />}
                            label={'Extended lock'}
                            labelPlacement={'start'}
                            checked={hasExtendedLock}
                            onChange={(e) => {
                                hasExtendedLock && setExtendedLockEnds(null)
                                setHasExtendedLock(!hasExtendedLock)
                            }}
                            disabled={!isActive || isLoading}
                        />
                        <DateTimePicker
                            value={extendedLockEnds}
                            onChange={(v) => setExtendedLockEnds(v)}
                            disabled={!isActive || isLoading || !hasExtendedLock}
                        />
                    </Stack>

                    {appContext?.identity()?.IsAdmin && reservable?.ExtendedLock && (
                        <Button
                            variant={'contained'}
                            color={'error'}
                            onClick={() => forceClearLock()}
                            size={'small'}
                            disabled={isLoading}
                        >
                            Force clear lock
                        </Button>
                    )}
                </Stack>
            </DialogContent>
        </CMDialog>
    )
}
